#!/usr/bin/env python3
"""
Test script for the enhanced prescription chatbot functionality
"""

import pandas as pd
import sys
import os

# Add the current directory to the path so we can import our module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_data_loading():
    """Test if data loads correctly"""
    print("Testing data loading...")
    try:
        # Import the load_data function
        from prescription_chatbot import load_data
        df = load_data()
        print(f"✅ Data loaded successfully: {len(df)} rows, {len(df.columns)} columns")
        print(f"   Columns: {list(df.columns)}")
        print(f"   Data types: {df.dtypes.to_dict()}")
        return df
    except Exception as e:
        print(f"❌ Data loading failed: {e}")
        return None

def test_query_parsing():
    """Test query parsing functionality"""
    print("\nTesting query parsing...")
    try:
        from prescription_chatbot import parse_query, get_unique_values
        
        # Test queries
        test_queries = [
            "Total prescriptions in Miami-Dade in 2022",
            "Average daily MME of oxycodone",
            "Standard deviation of quantity for alprazolam",
            "How many prescriptions in Broward county?",
            "Show me a chart of total MME by year"
        ]
        
        for query in test_queries:
            parsed = parse_query(query)
            print(f"   Query: '{query}'")
            print(f"   Parsed: {parsed}")
            print()
        
        print("✅ Query parsing working correctly")
        return True
    except Exception as e:
        print(f"❌ Query parsing failed: {e}")
        return False

def test_fuzzy_matching():
    """Test fuzzy matching functionality"""
    print("Testing fuzzy matching...")
    try:
        from prescription_chatbot import fuzzy_match
        
        # Test cases
        test_cases = [
            ("miami", ["MIAMI-DADE", "BROWARD", "PALM BEACH"]),
            ("oxycodone", ["oxycodone", "hydrocodone", "alprazolam"]),
            ("prescription", ["prescription_count", "daily_mme", "total_mme"])
        ]
        
        for query, options in test_cases:
            result = fuzzy_match(query, options)
            print(f"   Query: '{query}' -> Match: '{result}'")
        
        print("✅ Fuzzy matching working correctly")
        return True
    except Exception as e:
        print(f"❌ Fuzzy matching failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Enhanced Prescription Chatbot")
    print("=" * 50)
    
    # Test data loading
    df = test_data_loading()
    if df is None:
        print("❌ Cannot proceed without data")
        return
    
    # Test query parsing
    if not test_query_parsing():
        print("❌ Query parsing issues detected")
    
    # Test fuzzy matching
    if not test_fuzzy_matching():
        print("❌ Fuzzy matching issues detected")
    
    print("\n" + "=" * 50)
    print("🎉 Testing completed! The chatbot should now work much better.")
    print("\nKey improvements made:")
    print("✅ Fixed column name mapping")
    print("✅ Added proper data type conversion")
    print("✅ Implemented fuzzy matching for better query recognition")
    print("✅ Added support for statistical operations (mean, std, median, min, max)")
    print("✅ Enhanced visualization capabilities")
    print("✅ Improved error handling")
    print("✅ Added debug information")

if __name__ == "__main__":
    main()
