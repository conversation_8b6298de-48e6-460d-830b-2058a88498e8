import streamlit as st
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import re
import plotly.express as px
import json
import numpy as np
from difflib import get_close_matches
from typing import Dict, List, Any, Optional

st.set_page_config(page_title="Prescription Dispensation Chatbot", layout="wide")

# Load and cache data
@st.cache_data
def load_data():
    df = pd.read_csv("prescription_dispensation.csv", low_memory=False)

    # Clean column names
    df.columns = df.columns.str.strip()

    # Create a mapping for easier access
    column_mapping = {
        'Year of Filled At': 'year_of_filled_at',
        'Month of Filled At': 'month_of_filled_at',
        'Current Patient County': 'current_patient_county',
        'Concatenated Drug Flags, Benz, MR, Op, Stim (TJL)': 'drug_flags',
        'Drug Schedule': 'drug_schedule',
        'Generic Name': 'generic_name',
        'Age Groups (UF)': 'age_groups',
        'Prescription Count': 'prescription_count',
        'Daily MME': 'daily_mme',
        'Days Supply': 'days_supply',
        'Quantity': 'quantity',
        'Total MME': 'total_mme'
    }

    # Rename columns
    df = df.rename(columns=column_mapping)

    # Clean and convert numeric columns
    numeric_cols = ['prescription_count', 'daily_mme', 'days_supply', 'quantity', 'total_mme']
    for col in numeric_cols:
        if col in df.columns:
            # Remove commas and convert to numeric
            df[col] = pd.to_numeric(df[col].astype(str).str.replace(',', '').str.replace(' ', ''), errors='coerce')

    # Clean string columns
    string_cols = ['current_patient_county', 'drug_flags', 'generic_name', 'age_groups', 'month_of_filled_at']
    for col in string_cols:
        if col in df.columns:
            df[col] = df[col].astype(str).str.strip()

    return df

df = load_data()

# Helper functions for fuzzy matching
def fuzzy_match(query_term: str, options: List[str], threshold: float = 0.6) -> Optional[str]:
    """Find the best fuzzy match for a query term in a list of options."""
    if not query_term or not options:
        return None

    query_term = query_term.lower().strip()
    options_lower = [opt.lower().strip() for opt in options if pd.notna(opt)]

    # First try exact match
    if query_term in options_lower:
        return options[options_lower.index(query_term)]

    # Then try partial match
    for i, opt in enumerate(options_lower):
        if query_term in opt or opt in query_term:
            return options[i]

    # Finally try fuzzy match
    matches = get_close_matches(query_term, options_lower, n=1, cutoff=threshold)
    if matches:
        return options[options_lower.index(matches[0])]

    return None

def extract_numbers(text: str) -> List[float]:
    """Extract numbers from text."""
    numbers = re.findall(r'\b\d+(?:\.\d+)?\b', text)
    return [float(n) for n in numbers]

def detect_intent(text: str) -> str:
    """Detect the intent from the query text."""
    text = text.lower()

    # Statistical operations
    if any(word in text for word in ['standard deviation', 'std dev', 'std', 'deviation']):
        return 'std'
    elif any(word in text for word in ['median', 'middle']):
        return 'median'
    elif any(word in text for word in ['average', 'mean', 'avg']):
        return 'mean'
    elif any(word in text for word in ['total', 'sum', 'add up']):
        return 'sum'
    elif any(word in text for word in ['minimum', 'min', 'lowest', 'smallest']):
        return 'min'
    elif any(word in text for word in ['maximum', 'max', 'highest', 'largest', 'most']):
        return 'max'
    elif any(word in text for word in ['count', 'number of', 'how many']):
        return 'count'

    # Visualization
    elif any(word in text for word in ['chart', 'plot', 'graph', 'visualize', 'show trend']):
        return 'plot'
    elif any(word in text for word in ['table', 'list', 'show all', 'breakdown']):
        return 'table'
    elif any(word in text for word in ['map', 'geographic', 'spatial']):
        return 'map'
    elif any(word in text for word in ['compare', 'comparison', 'versus', 'vs']):
        return 'compare'

    # Default
    return 'count'

# Sidebar
with st.sidebar:
    st.title("🔎 Help")
    st.markdown("""
    **Example Queries:**
    - Total MME in Miami-Dade in 2022
    - Average daily MME of oxycodone in 2021
    - Standard deviation of prescription count in 2023
    - What counties had the most prescriptions in 2020?
    - Chart of total MME by year for benzodiazepines
    - Compare total MME of oxycodone and hydrocodone
    - Table of total prescriptions by county
    - Median quantity for alprazolam
    - Show me prescriptions for opioids in Broward
    - How many prescriptions in January 2022?

    **Supported Operations:**
    - Sum, Average, Median, Standard Deviation
    - Min, Max, Count
    - Charts, Tables, Comparisons
    - Fuzzy matching for drug names and counties
    """)

# Header
st.markdown("""
<h1 style='text-align:center; color:#0021A5;'>💊 Prescription Dispensation Query Chatbot</h1>
""", unsafe_allow_html=True)

# User input
user_input = st.text_input("Ask a question about prescription data:", "")

# Extract unique values for matching
@st.cache_data
def get_unique_values():
    return {
        'drug_flags': sorted([str(x) for x in df['drug_flags'].dropna().unique()]),
        'generic_names': sorted([str(x) for x in df['generic_name'].dropna().unique()]),
        'counties': sorted([str(x) for x in df['current_patient_county'].dropna().unique()]),
        'age_groups': sorted([str(x) for x in df['age_groups'].dropna().unique()]),
        'years': sorted([int(x) for x in df['year_of_filled_at'].dropna().unique()]),
        'months': sorted([str(x) for x in df['month_of_filled_at'].dropna().unique()]),
        'metrics': ['prescription_count', 'daily_mme', 'days_supply', 'quantity', 'total_mme']
    }

unique_vals = get_unique_values()

# Enhanced query parser
def parse_query(text: str) -> Dict[str, Any]:
    """Parse natural language query into structured format."""
    text_lower = text.lower()

    # Extract numbers (years)
    numbers = extract_numbers(text)
    years_in_text = [n for n in numbers if 2020 <= n <= 2030]

    # Use fuzzy matching for better entity recognition
    query = {
        "year": years_in_text[0] if years_in_text else None,
        "month": fuzzy_match(text_lower, unique_vals['months']),
        "county": fuzzy_match(text_lower, unique_vals['counties']),
        "flag": fuzzy_match(text_lower, unique_vals['drug_flags']),
        "drug": fuzzy_match(text_lower, unique_vals['generic_names']),
        "age": fuzzy_match(text_lower, unique_vals['age_groups']),
        "metric": None,
        "intent": detect_intent(text)
    }

    # Detect metric with fuzzy matching and synonyms
    metric_synonyms = {
        'prescription_count': ['prescription', 'prescriptions', 'rx', 'count', 'number'],
        'daily_mme': ['daily mme', 'mme daily', 'daily', 'mme per day'],
        'days_supply': ['days supply', 'supply', 'days', 'duration'],
        'quantity': ['quantity', 'amount', 'qty', 'units'],
        'total_mme': ['total mme', 'mme total', 'mme', 'morphine equivalent']
    }

    for metric, synonyms in metric_synonyms.items():
        if any(syn in text_lower for syn in synonyms):
            query["metric"] = metric
            break

    # Default metric based on intent
    if not query["metric"]:
        if query["intent"] in ['sum', 'mean', 'std', 'median', 'min', 'max']:
            query["metric"] = 'prescription_count'  # Default metric

    return query

# Enhanced query handler
def handle_query(q: Dict[str, Any]) -> Optional[str]:
    """Handle the parsed query and return results."""
    dff = df.copy()

    # Apply filters with case-insensitive matching
    if q["year"]:
        dff = dff[dff['year_of_filled_at'] == q["year"]]
    if q["month"]:
        dff = dff[dff['month_of_filled_at'].str.lower() == q["month"].lower()]
    if q["county"]:
        dff = dff[dff['current_patient_county'].str.lower() == q["county"].lower()]
    if q["flag"]:
        dff = dff[dff['drug_flags'].str.lower() == q["flag"].lower()]
    if q["drug"]:
        dff = dff[dff['generic_name'].str.lower() == q["drug"].lower()]
    if q["age"]:
        dff = dff[dff['age_groups'].str.lower() == q["age"].lower()]

    if dff.empty:
        return "🚫 No matching records found for your query."

    # Handle different intents
    if q["intent"] == "count":
        return f"✅ Found {len(dff):,} matching records."

    if not q["metric"]:
        return f"✅ Found {len(dff):,} matching records. Please specify a metric for calculations."

    # Get the metric column
    metric_col = dff[q["metric"]].dropna()

    if metric_col.empty:
        return f"🚫 No valid {q['metric'].replace('_', ' ')} data found."

    # Statistical operations
    if q["intent"] == "sum":
        result = metric_col.sum()
        return f"🧮 Total {q['metric'].replace('_', ' ')}: {result:,.2f}"

    elif q["intent"] == "mean":
        result = metric_col.mean()
        return f"📊 Average {q['metric'].replace('_', ' ')}: {result:,.2f}"

    elif q["intent"] == "median":
        result = metric_col.median()
        return f"📊 Median {q['metric'].replace('_', ' ')}: {result:,.2f}"

    elif q["intent"] == "std":
        result = metric_col.std()
        return f"📊 Standard deviation of {q['metric'].replace('_', ' ')}: {result:,.2f}"

    elif q["intent"] == "min":
        result = metric_col.min()
        return f"📊 Minimum {q['metric'].replace('_', ' ')}: {result:,.2f}"

    elif q["intent"] == "max":
        result = metric_col.max()
        return f"📊 Maximum {q['metric'].replace('_', ' ')}: {result:,.2f}"

    # Visualizations
    elif q["intent"] == "plot":
        st.subheader("📈 Trend Analysis")

        # Group by year for trend
        if 'year_of_filled_at' in dff.columns:
            trend = dff.groupby("year_of_filled_at")[q['metric']].sum().reset_index()

            if not trend.empty:
                fig, ax = plt.subplots(figsize=(10, 6))
                sns.lineplot(data=trend, x="year_of_filled_at", y=q['metric'],
                           ax=ax, color="#0021A5", marker="o", linewidth=2, markersize=8)
                ax.set_title(f"{q['metric'].replace('_', ' ').title()} by Year",
                           color="#FA4616", fontsize=16, fontweight='bold')
                ax.set_xlabel("Year", fontsize=12)
                ax.set_ylabel(q['metric'].replace('_', ' ').title(), fontsize=12)
                ax.grid(True, alpha=0.3)
                plt.tight_layout()
                st.pyplot(fig)
                return None
            else:
                return "🚫 No data available for plotting."

    elif q["intent"] == "table":
        st.subheader("📋 Data Table")

        # Group by county by default, or by available categorical column
        group_col = 'current_patient_county'
        if q["county"]:  # If county is filtered, group by drug or flag
            if q["drug"]:
                group_col = 'generic_name'
            elif q["flag"]:
                group_col = 'drug_flags'
            else:
                group_col = 'age_groups'

        if group_col in dff.columns:
            table = dff.groupby(group_col)[q['metric']].agg(['sum', 'mean', 'count']).reset_index()
            table.columns = [group_col.replace('_', ' ').title(), 'Total', 'Average', 'Count']
            table = table.sort_values('Total', ascending=False).head(20)
            st.dataframe(table, use_container_width=True)
            return None
        else:
            return "🚫 Unable to create table with current filters."

    elif q["intent"] == "compare":
        st.subheader("📊 Comparison Analysis")

        # Compare by drug if multiple drugs mentioned, otherwise by county
        if len(dff['generic_name'].unique()) > 1:
            comparison = dff.groupby('generic_name')[q['metric']].sum().sort_values(ascending=False).head(10)

            fig, ax = plt.subplots(figsize=(12, 6))
            comparison.plot(kind='bar', ax=ax, color='#0021A5')
            ax.set_title(f"Comparison of {q['metric'].replace('_', ' ').title()} by Drug",
                        color="#FA4616", fontsize=16, fontweight='bold')
            ax.set_xlabel("Generic Name", fontsize=12)
            ax.set_ylabel(q['metric'].replace('_', ' ').title(), fontsize=12)
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()
            st.pyplot(fig)
            return None
        else:
            comparison = dff.groupby('current_patient_county')[q['metric']].sum().sort_values(ascending=False).head(10)

            fig, ax = plt.subplots(figsize=(12, 6))
            comparison.plot(kind='bar', ax=ax, color='#0021A5')
            ax.set_title(f"Comparison of {q['metric'].replace('_', ' ').title()} by County",
                        color="#FA4616", fontsize=16, fontweight='bold')
            ax.set_xlabel("County", fontsize=12)
            ax.set_ylabel(q['metric'].replace('_', ' ').title(), fontsize=12)
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()
            st.pyplot(fig)
            return None

    return f"✅ Found {len(dff):,} matching records."

# Enhanced execution logic
if user_input:
    with st.spinner("🔍 Analyzing your query..."):
        try:
            query = parse_query(user_input)

            # Show parsed query for debugging (optional)
            with st.expander("🔧 Query Analysis (Debug Info)", expanded=False):
                st.json(query)

            response = handle_query(query)

            if response:
                st.success(response)

            # Show summary statistics if data was filtered
            if any([query["year"], query["month"], query["county"], query["flag"], query["drug"], query["age"]]):
                st.info("💡 **Query Summary:** " +
                       ", ".join([f"{k}: {v}" for k, v in query.items()
                                if v is not None and k not in ["intent", "metric"]]))

        except Exception as e:
            st.error(f"❌ An error occurred: {str(e)}")
            st.info("💡 Try rephrasing your query or check the example queries in the sidebar.")

# Display data info
with st.expander("📊 Dataset Information", expanded=False):
    st.write(f"**Total Records:** {len(df):,}")
    st.write(f"**Date Range:** {df['year_of_filled_at'].min()} - {df['year_of_filled_at'].max()}")
    st.write(f"**Counties:** {df['current_patient_county'].nunique()}")
    st.write(f"**Unique Drugs:** {df['generic_name'].nunique()}")
    st.write(f"**Drug Categories:** {df['drug_flags'].nunique()}")

    col1, col2 = st.columns(2)
    with col1:
        st.write("**Top 5 Counties by Records:**")
        top_counties = df['current_patient_county'].value_counts().head()
        st.write(top_counties)

    with col2:
        st.write("**Top 5 Drugs by Records:**")
        top_drugs = df['generic_name'].value_counts().head()
        st.write(top_drugs)
