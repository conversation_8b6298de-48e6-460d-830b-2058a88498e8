import streamlit as st
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import re
import plotly.express as px
import json

st.set_page_config(page_title="Prescription Dispensation Chatbot", layout="wide")

# Load and cache data
@st.cache_data
def load_data():
    df = pd.read_csv("prescription_dispensation.csv", low_memory=False)
    df.columns = df.columns.str.strip().str.lower().str.replace(" ", "_")
    return df

df = load_data()

# Sidebar
with st.sidebar:
    st.title("🔎 Help")
    st.markdown("""
    **Example Queries:**
    - Total MME in Miami-Dade in 2022
    - Average daily MME of oxycodone in 2021
    - What counties had the most prescriptions in 2020?
    - Chart of total MME by year for benzodiazepines
    - Compare total MME of oxycodone and hydrocodone
    - Table of total prescriptions by county
    - Total MME in January 2022
    """)

# Header
st.markdown("""
<h1 style='text-align:center; color:#0021A5;'>💊 Prescription Dispensation Query Chatbot</h1>
""", unsafe_allow_html=True)

# User input
user_input = st.text_input("Ask a question about prescription data:", "")

# Identify options
drug_flags = df['concatenated_drug_flags,_benz,_mr,_op,_stim_(tjl)'].dropna().unique()
generic_names = df['generic_name'].dropna().unique()
counties = df['current_patient_county'].dropna().unique()
age_groups = df['age_groups_(uf)'].dropna().unique()
metrics = ['prescription_count', 'daily_mme', 'days_supply', 'quantity', 'total_mme']
years = sorted(df['year_of_filled_at'].dropna().unique())
months = sorted(df['month_of_filled_at'].dropna().unique())

# Query parser
def parse_query(text):
    text = text.lower()
    query = {
        "year": next((int(y) for y in map(str, years) if y in text), None),
        "month": next((m for m in months if m.lower() in text), None),
        "county": next((c for c in counties if c.lower() in text), None),
        "flag": next((f for f in drug_flags if f.lower() in text), None),
        "drug": next((g for g in generic_names if g.lower() in text), None),
        "age": next((a for a in age_groups if a.lower() in text), None),
        "metric": next((m for m in metrics if m.replace("_", " ") in text or m in text), None),
    }
    query["intent"] = "count"
    if "chart" in text or "plot" in text or "graph" in text:
        query["intent"] = "plot"
    elif "map" in text:
        query["intent"] = "map"
    elif "compare" in text:
        query["intent"] = "compare"
    elif "table" in text:
        query["intent"] = "table"
    elif "average" in text or "mean" in text:
        query["intent"] = "mean"
    elif "total" in text or "sum" in text:
        query["intent"] = "sum"
    return query

# Handle query
def handle_query(q):
    dff = df.copy()
    if q["year"]:
        dff = dff[dff['year_of_filled_at'] == q["year"]]
    if q["month"]:
        dff = dff[dff['month_of_filled_at'].str.lower() == q["month"]]
    if q["county"]:
        dff = dff[dff['current_patient_county'].str.lower() == q["county"]]
    if q["flag"]:
        dff = dff[dff['concatenated_drug_flags,_benz,_mr,_op,_stim_(tjl)'].str.lower() == q["flag"]]
    if q["drug"]:
        dff = dff[dff['generic_name'].str.lower() == q["drug"]]
    if q["age"]:
        dff = dff[dff['age_groups_(uf)'].str.lower() == q["age"]]

    if dff.empty:
        return "🚫 No matching records found."

    if q["intent"] == "sum" and q["metric"]:
        return f"🧮 Total {q['metric'].replace('_', ' ')}: {dff[q['metric']].sum():,.2f}"
    elif q["intent"] == "mean" and q["metric"]:
        return f"📊 Average {q['metric'].replace('_', ' ')}: {dff[q['metric']].mean():,.2f}"
    elif q["intent"] == "table" and q["metric"]:
        table = dff.groupby(["current_patient_county"])[q['metric']].sum().reset_index()
        st.dataframe(table)
    elif q["intent"] == "plot" and q["metric"]:
        st.subheader("📈 Yearly Trend")
        trend = dff.groupby("year_of_filled_at")[q['metric']].sum().reset_index()
        fig, ax = plt.subplots()
        sns.lineplot(data=trend, x="year_of_filled_at", y=q['metric'], ax=ax, color="#0021A5", marker="o")
        ax.set_title(f"{q['metric'].replace('_', ' ').title()} by Year", color="#FA4616")
        st.pyplot(fig)
    elif q["intent"] == "count":
        return f"✅ {len(dff):,} matching records found."
    return None

# Run logic
if user_input:
    query = parse_query(user_input)
    response = handle_query(query)
    if response:
        st.success(response)
